{"sources": [{"file": "F:/kfa/codexm/station-code/build/CMakeFiles/bootloader"}, {"file": "F:/kfa/codexm/station-code/build/CMakeFiles/bootloader.rule"}, {"file": "F:/kfa/codexm/station-code/build/CMakeFiles/bootloader-complete.rule"}, {"file": "F:/kfa/codexm/station-code/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "F:/kfa/codexm/station-code/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "F:/kfa/codexm/station-code/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "F:/kfa/codexm/station-code/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "F:/kfa/codexm/station-code/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "F:/kfa/codexm/station-code/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "F:/kfa/codexm/station-code/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}