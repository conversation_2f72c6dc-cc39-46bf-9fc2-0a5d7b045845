# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.20

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: bootloader
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja


#############################################
# Utility command for _project_elf_src

build _project_elf_src: phony CMakeFiles/_project_elf_src project_elf_src_esp32c3.c


#############################################
# Utility command for confserver

build confserver: phony CMakeFiles/confserver


#############################################
# Utility command for erase_flash

build erase_flash: phony CMakeFiles/erase_flash


#############################################
# Utility command for app

build app: phony CMakeFiles/app esp-idf/esptool_py/bootloader_check_size gen_project_binary


#############################################
# Utility command for menuconfig

build menuconfig: phony CMakeFiles/menuconfig


#############################################
# Utility command for gen_project_binary

build gen_project_binary: phony CMakeFiles/gen_project_binary .bin_timestamp bootloader.elf

# =============================================================================
# Object build statements for EXECUTABLE target bootloader.elf


#############################################
# Order-only phony target for bootloader.elf

build cmake_object_order_depends_target_bootloader.elf: phony || _project_elf_src cmake_object_order_depends_target___idf_hal cmake_object_order_depends_target___idf_main project_elf_src_esp32c3.c

build CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c3.c.obj: C_COMPILER__bootloader.2eelf_ project_elf_src_esp32c3.c || cmake_object_order_depends_target_bootloader.elf
  DEP_FILE = CMakeFiles\bootloader.elf.dir\project_elf_src_esp32c3.c.obj.d
  FLAGS = -march=rv32imc
  INCLUDES = -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = CMakeFiles\bootloader.elf.dir
  OBJECT_FILE_DIR = CMakeFiles\bootloader.elf.dir
  TARGET_COMPILE_PDB = CMakeFiles\bootloader.elf.dir\
  TARGET_PDB = bootloader.elf.pdb


# =============================================================================
# Link build statements for EXECUTABLE target bootloader.elf


#############################################
# Link the executable bootloader.elf

build bootloader.elf: C_EXECUTABLE_LINKER__bootloader.2eelf_ CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c3.c.obj | esp-idf/hal/libhal.a esp-idf/soc/libsoc.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/spi_flash/libspi_flash.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_system/libesp_system.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_common/libesp_common.a esp-idf/esp_rom/libesp_rom.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/hal/libhal.a esp-idf/soc/libsoc.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/spi_flash/libspi_flash.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_system/libesp_system.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_common/libesp_common.a esp-idf/esp_rom/libesp_rom.a esp-idf/log/liblog.a esp-idf/hal/libhal.a esp-idf/soc/libsoc.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/spi_flash/libspi_flash.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_system/libesp_system.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_common/libesp_common.a esp-idf/esp_rom/libesp_rom.a esp-idf/log/liblog.a esp-idf/hal/libhal.a esp-idf/soc/libsoc.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/spi_flash/libspi_flash.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_system/libesp_system.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_common/libesp_common.a esp-idf/esp_rom/libesp_rom.a esp-idf/log/liblog.a esp-idf/hal/libhal.a esp-idf/soc/libsoc.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/spi_flash/libspi_flash.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_system/libesp_system.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_common/libesp_common.a esp-idf/esp_rom/libesp_rom.a esp-idf/log/liblog.a F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/ld/esp32c3.peripherals.ld F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3/ld/esp32c3.rom.ld F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3/ld/esp32c3.rom.api.ld F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3/ld/esp32c3.rom.libgcc.ld F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3/ld/esp32c3.rom.newlib.ld F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/main/ld/esp32c3/bootloader.ld F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/main/ld/esp32c3/bootloader.rom.ld || _project_elf_src esp-idf/hal/libhal.a esp-idf/main/libmain.a
  FLAGS = -march=rv32imc
  LINK_FLAGS = -nostartfiles -march=rv32imc --specs=nosys.specs
  LINK_LIBRARIES = esp-idf/hal/libhal.a  esp-idf/soc/libsoc.a  esp-idf/micro-ecc/libmicro-ecc.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/efuse/libefuse.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/log/liblog.a  esp-idf/main/libmain.a  -Wl,--cref  -Wl,--Map="F:/kfa/codexm/station-code/build/bootloader/bootloader.map"  -Wl,--gc-sections  -fno-rtti  -fno-lto  esp-idf/hal/libhal.a  esp-idf/soc/libsoc.a  esp-idf/micro-ecc/libmicro-ecc.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/efuse/libefuse.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/log/liblog.a  esp-idf/hal/libhal.a  esp-idf/soc/libsoc.a  esp-idf/micro-ecc/libmicro-ecc.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/efuse/libefuse.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/log/liblog.a  esp-idf/hal/libhal.a  esp-idf/soc/libsoc.a  esp-idf/micro-ecc/libmicro-ecc.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/efuse/libefuse.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/log/liblog.a  esp-idf/hal/libhal.a  esp-idf/soc/libsoc.a  esp-idf/micro-ecc/libmicro-ecc.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/efuse/libefuse.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/log/liblog.a  -u __assert_func  -L "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/ld"  -T esp32c3.peripherals.ld  -u abort  -u __ubsan_include  -L "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3/ld"  -T esp32c3.rom.ld  -T esp32c3.rom.api.ld  -T esp32c3.rom.libgcc.ld  -T esp32c3.rom.newlib.ld  -L "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/main/ld/esp32c3"  -T bootloader.ld  -T bootloader.rom.ld  -u bootloader_hooks_include
  OBJECT_DIR = CMakeFiles\bootloader.elf.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\bootloader.elf.dir\
  TARGET_FILE = bootloader.elf
  TARGET_PDB = bootloader.elf.pdb


#############################################
# Utility command for monitor

build monitor: phony CMakeFiles/monitor


#############################################
# Utility command for size

build size: phony CMakeFiles/size


#############################################
# Utility command for size-files

build size-files: phony CMakeFiles/size-files


#############################################
# Utility command for size-components

build size-components: phony CMakeFiles/size-components


#############################################
# Utility command for uf2-app

build uf2-app: phony CMakeFiles/uf2-app gen_project_binary


#############################################
# Utility command for uf2

build uf2: phony CMakeFiles/uf2 gen_project_binary


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Phony custom command for CMakeFiles\_project_elf_src

build CMakeFiles/_project_elf_src: phony project_elf_src_esp32c3.c


#############################################
# Custom command for project_elf_src_esp32c3.c

build project_elf_src_esp32c3.c: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe -E touch F:/kfa/codexm/station-code/build/bootloader/project_elf_src_esp32c3.c"
  DESC = Generating project_elf_src_esp32c3.c
  restat = 1


#############################################
# Custom command for CMakeFiles\confserver

build CMakeFiles/confserver: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader && F:\kfa\Espressif\python_env\idf4.4_py3.8_env\Scripts\python.exe F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/kconfig_new/prepare_kconfig_files.py --env-file F:/kfa/codexm/station-code/build/bootloader/config.env && F:\kfa\Espressif\python_env\idf4.4_py3.8_env\Scripts\python.exe F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/kconfig_new/confserver.py --env-file F:/kfa/codexm/station-code/build/bootloader/config.env --kconfig F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/Kconfig --sdkconfig-rename F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/sdkconfig.rename --config F:/kfa/codexm/station-code/sdkconfig"
  pool = console


#############################################
# Custom command for CMakeFiles\erase_flash

build CMakeFiles/erase_flash: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\esptool_py && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe -D IDF_PATH="F:/kfa/Espressif/frameworks/esp-idf-v4.4.1" -D SERIAL_TOOL="F:/kfa/Espressif/python_env/idf4.4_py3.8_env/Scripts/python.exe F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esptool_py/esptool/esptool.py --chip esp32c3" -D SERIAL_TOOL_ARGS="erase_flash" -P run_serial_tool.cmake"
  pool = console


#############################################
# Phony custom command for CMakeFiles\app

build CMakeFiles/app: phony || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/esptool_py/bootloader_check_size esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/spi_flash/libspi_flash.a gen_project_binary


#############################################
# Custom command for CMakeFiles\menuconfig

build CMakeFiles/menuconfig: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader && F:\kfa\Espressif\python_env\idf4.4_py3.8_env\Scripts\python.exe F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/kconfig_new/prepare_kconfig_files.py --env-file F:/kfa/codexm/station-code/build/bootloader/config.env && F:\kfa\Espressif\python_env\idf4.4_py3.8_env\Scripts\python.exe F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/kconfig_new/confgen.py --kconfig F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/Kconfig --sdkconfig-rename F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/sdkconfig.rename --config F:/kfa/codexm/station-code/sdkconfig --env-file F:/kfa/codexm/station-code/build/bootloader/config.env --env IDF_TARGET=esp32c3 --env IDF_ENV_FPGA= --dont-write-deprecated --output config F:/kfa/codexm/station-code/sdkconfig && F:\kfa\Espressif\python_env\idf4.4_py3.8_env\Scripts\python.exe F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/check_term.py && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe -E env COMPONENT_KCONFIGS_SOURCE_FILE=F:/kfa/codexm/station-code/build/bootloader/kconfigs.in COMPONENT_KCONFIGS_PROJBUILD_SOURCE_FILE=F:/kfa/codexm/station-code/build/bootloader/kconfigs_projbuild.in IDF_CMAKE=y KCONFIG_CONFIG=F:/kfa/codexm/station-code/sdkconfig IDF_TARGET=esp32c3 IDF_ENV_FPGA= F:/kfa/Espressif/python_env/idf4.4_py3.8_env/Scripts/python.exe -m menuconfig F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/Kconfig && F:\kfa\Espressif\python_env\idf4.4_py3.8_env\Scripts\python.exe F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/kconfig_new/confgen.py --kconfig F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/Kconfig --sdkconfig-rename F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/sdkconfig.rename --config F:/kfa/codexm/station-code/sdkconfig --env-file F:/kfa/codexm/station-code/build/bootloader/config.env --env IDF_TARGET=esp32c3 --env IDF_ENV_FPGA= --output config F:/kfa/codexm/station-code/sdkconfig"
  pool = console


#############################################
# Phony custom command for CMakeFiles\gen_project_binary

build CMakeFiles/gen_project_binary: phony .bin_timestamp || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/spi_flash/libspi_flash.a


#############################################
# Custom command for .bin_timestamp

build .bin_timestamp: CUSTOM_COMMAND bootloader.elf || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/spi_flash/libspi_flash.a
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader && F:\kfa\Espressif\python_env\idf4.4_py3.8_env\Scripts\python.exe F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esptool_py/esptool/esptool.py --chip esp32c3 elf2image --flash_mode dio --flash_freq 80m --flash_size 2MB --min-rev 3 -o F:/kfa/codexm/station-code/build/bootloader/bootloader.bin F:/kfa/codexm/station-code/build/bootloader/bootloader.elf && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe -E echo "Generated F:/kfa/codexm/station-code/build/bootloader/bootloader.bin" && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe -E md5sum F:/kfa/codexm/station-code/build/bootloader/bootloader.bin > F:/kfa/codexm/station-code/build/bootloader/.bin_timestamp"
  DESC = Generating binary image from built executable
  restat = 1


#############################################
# Custom command for CMakeFiles\monitor

build CMakeFiles/monitor: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\esptool_py && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe -D IDF_PATH="F:/kfa/Espressif/frameworks/esp-idf-v4.4.1" -D SERIAL_TOOL="F:/kfa/Espressif/python_env/idf4.4_py3.8_env/Scripts/python.exe F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/idf_monitor.py" -D SERIAL_TOOL_ARGS="--target esp32c3 --revision 3 F:/kfa/codexm/station-code/build/bootloader/bootloader.elf" -D WORKING_DIRECTORY="F:/kfa/codexm/station-code/build/bootloader" -P run_serial_tool.cmake"
  pool = console


#############################################
# Custom command for CMakeFiles\size

build CMakeFiles/size: CUSTOM_COMMAND bootloader.map
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader && F:\kfa\Espressif\python_env\idf4.4_py3.8_env\Scripts\python.exe F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/idf_size.py F:/kfa/codexm/station-code/build/bootloader/bootloader.map"


#############################################
# Custom command for CMakeFiles\size-files

build CMakeFiles/size-files: CUSTOM_COMMAND bootloader.map
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader && F:\kfa\Espressif\python_env\idf4.4_py3.8_env\Scripts\python.exe F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/idf_size.py --files F:/kfa/codexm/station-code/build/bootloader/bootloader.map"


#############################################
# Custom command for CMakeFiles\size-components

build CMakeFiles/size-components: CUSTOM_COMMAND bootloader.map
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader && F:\kfa\Espressif\python_env\idf4.4_py3.8_env\Scripts\python.exe F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/idf_size.py --archives F:/kfa/codexm/station-code/build/bootloader/bootloader.map"


#############################################
# Custom command for CMakeFiles\uf2-app

build CMakeFiles/uf2-app: CUSTOM_COMMAND || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/spi_flash/libspi_flash.a gen_project_binary
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader && F:\kfa\Espressif\python_env\idf4.4_py3.8_env\Scripts\python.exe F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/mkuf2.py write -o F:/kfa/codexm/station-code/build/bootloader/uf2-app.bin --json F:/kfa/codexm/station-code/build/bootloader/flasher_args.json --chip-id 0xd42ba06c --bin app"
  pool = console


#############################################
# Custom command for CMakeFiles\uf2

build CMakeFiles/uf2: CUSTOM_COMMAND bootloader || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/spi_flash/libspi_flash.a gen_project_binary
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader && F:\kfa\Espressif\python_env\idf4.4_py3.8_env\Scripts\python.exe F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/mkuf2.py write -o F:/kfa/codexm/station-code/build/bootloader/uf2.bin --json F:/kfa/codexm/station-code/build/bootloader/flasher_args.json --chip-id 0xd42ba06c"
  pool = console

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/edit_cache: phony esp-idf/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/rebuild_cache: phony esp-idf/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/newlib/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\newlib && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/newlib/edit_cache: phony esp-idf/newlib/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/newlib/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\newlib && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/newlib/rebuild_cache: phony esp-idf/newlib/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_hal


#############################################
# Order-only phony target for __idf_hal

build cmake_object_order_depends_target___idf_hal: phony || cmake_object_order_depends_target___idf_soc

build esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj: C_COMPILER____idf_hal_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/wdt_hal_iram.c || cmake_object_order_depends_target___idf_hal
  DEP_FILE = esp-idf\hal\CMakeFiles\__idf_hal.dir\wdt_hal_iram.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include
  OBJECT_DIR = esp-idf\hal\CMakeFiles\__idf_hal.dir
  OBJECT_FILE_DIR = esp-idf\hal\CMakeFiles\__idf_hal.dir
  TARGET_COMPILE_PDB = esp-idf\hal\CMakeFiles\__idf_hal.dir\__idf_hal.pdb
  TARGET_PDB = esp-idf\hal\libhal.pdb

build esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj: C_COMPILER____idf_hal_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/mpu_hal.c || cmake_object_order_depends_target___idf_hal
  DEP_FILE = esp-idf\hal\CMakeFiles\__idf_hal.dir\mpu_hal.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include
  OBJECT_DIR = esp-idf\hal\CMakeFiles\__idf_hal.dir
  OBJECT_FILE_DIR = esp-idf\hal\CMakeFiles\__idf_hal.dir
  TARGET_COMPILE_PDB = esp-idf\hal\CMakeFiles\__idf_hal.dir\__idf_hal.pdb
  TARGET_PDB = esp-idf\hal\libhal.pdb

build esp-idf/hal/CMakeFiles/__idf_hal.dir/cpu_hal.c.obj: C_COMPILER____idf_hal_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/cpu_hal.c || cmake_object_order_depends_target___idf_hal
  DEP_FILE = esp-idf\hal\CMakeFiles\__idf_hal.dir\cpu_hal.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include
  OBJECT_DIR = esp-idf\hal\CMakeFiles\__idf_hal.dir
  OBJECT_FILE_DIR = esp-idf\hal\CMakeFiles\__idf_hal.dir
  TARGET_COMPILE_PDB = esp-idf\hal\CMakeFiles\__idf_hal.dir\__idf_hal.pdb
  TARGET_PDB = esp-idf\hal\libhal.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_hal


#############################################
# Link the static library esp-idf\hal\libhal.a

build esp-idf/hal/libhal.a: C_STATIC_LIBRARY_LINKER____idf_hal_ esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj esp-idf/hal/CMakeFiles/__idf_hal.dir/cpu_hal.c.obj || esp-idf/soc/libsoc.a
  LANGUAGE_COMPILE_FLAGS = -march=rv32imc
  OBJECT_DIR = esp-idf\hal\CMakeFiles\__idf_hal.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\hal\CMakeFiles\__idf_hal.dir\__idf_hal.pdb
  TARGET_FILE = esp-idf\hal\libhal.a
  TARGET_PDB = esp-idf\hal\libhal.pdb


#############################################
# Utility command for edit_cache

build esp-idf/hal/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\hal && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/hal/edit_cache: phony esp-idf/hal/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/hal/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\hal && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/hal/rebuild_cache: phony esp-idf/hal/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_soc


#############################################
# Order-only phony target for __idf_soc

build cmake_object_order_depends_target___idf_soc: phony || cmake_object_order_depends_target___idf_micro-ecc

build esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj: C_COMPILER____idf_soc_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/lldesc.c || cmake_object_order_depends_target___idf_soc
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\lldesc.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/soc_include_legacy_warn.c.obj: C_COMPILER____idf_soc_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/soc_include_legacy_warn.c || cmake_object_order_depends_target___idf_soc
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\soc_include_legacy_warn.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/adc_periph.c.obj: C_COMPILER____idf_soc_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/adc_periph.c || cmake_object_order_depends_target___idf_soc
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3\adc_periph.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/dedic_gpio_periph.c.obj: C_COMPILER____idf_soc_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/dedic_gpio_periph.c || cmake_object_order_depends_target___idf_soc
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3\dedic_gpio_periph.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gdma_periph.c.obj: C_COMPILER____idf_soc_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/gdma_periph.c || cmake_object_order_depends_target___idf_soc
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3\gdma_periph.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gpio_periph.c.obj: C_COMPILER____idf_soc_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/gpio_periph.c || cmake_object_order_depends_target___idf_soc
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3\gpio_periph.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/interrupts.c.obj: C_COMPILER____idf_soc_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/interrupts.c || cmake_object_order_depends_target___idf_soc
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3\interrupts.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/spi_periph.c.obj: C_COMPILER____idf_soc_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/spi_periph.c || cmake_object_order_depends_target___idf_soc
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3\spi_periph.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/ledc_periph.c.obj: C_COMPILER____idf_soc_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/ledc_periph.c || cmake_object_order_depends_target___idf_soc
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3\ledc_periph.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/rmt_periph.c.obj: C_COMPILER____idf_soc_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/rmt_periph.c || cmake_object_order_depends_target___idf_soc
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3\rmt_periph.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/sigmadelta_periph.c.obj: C_COMPILER____idf_soc_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/sigmadelta_periph.c || cmake_object_order_depends_target___idf_soc
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3\sigmadelta_periph.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2s_periph.c.obj: C_COMPILER____idf_soc_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/i2s_periph.c || cmake_object_order_depends_target___idf_soc
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3\i2s_periph.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2c_periph.c.obj: C_COMPILER____idf_soc_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/i2c_periph.c || cmake_object_order_depends_target___idf_soc
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3\i2c_periph.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/uart_periph.c.obj: C_COMPILER____idf_soc_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/uart_periph.c || cmake_object_order_depends_target___idf_soc
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3\uart_periph.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/timer_periph.c.obj: C_COMPILER____idf_soc_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/timer_periph.c || cmake_object_order_depends_target___idf_soc
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3\timer_periph.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32c3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_soc


#############################################
# Link the static library esp-idf\soc\libsoc.a

build esp-idf/soc/libsoc.a: C_STATIC_LIBRARY_LINKER____idf_soc_ esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/soc_include_legacy_warn.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/adc_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/dedic_gpio_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gdma_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gpio_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/interrupts.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/spi_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/ledc_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/rmt_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/sigmadelta_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2s_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2c_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/uart_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/timer_periph.c.obj || esp-idf/micro-ecc/libmicro-ecc.a
  LANGUAGE_COMPILE_FLAGS = -march=rv32imc
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_FILE = esp-idf\soc\libsoc.a
  TARGET_PDB = esp-idf\soc\libsoc.pdb


#############################################
# Utility command for edit_cache

build esp-idf/soc/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\soc && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/soc/edit_cache: phony esp-idf/soc/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/soc/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\soc && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/soc/rebuild_cache: phony esp-idf/soc/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/soc/esp32c3/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\soc\esp32c3 && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/soc/esp32c3/edit_cache: phony esp-idf/soc/esp32c3/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/soc/esp32c3/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\soc\esp32c3 && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/soc/esp32c3/rebuild_cache: phony esp-idf/soc/esp32c3/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_micro-ecc


#############################################
# Order-only phony target for __idf_micro-ecc

build cmake_object_order_depends_target___idf_micro-ecc: phony || cmake_object_order_depends_target___idf_spi_flash

build esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj: C_COMPILER____idf_micro-ecc_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/uECC_verify_antifault.c || cmake_object_order_depends_target___idf_micro-ecc
  DEP_FILE = esp-idf\micro-ecc\CMakeFiles\__idf_micro-ecc.dir\uECC_verify_antifault.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include
  OBJECT_DIR = esp-idf\micro-ecc\CMakeFiles\__idf_micro-ecc.dir
  OBJECT_FILE_DIR = esp-idf\micro-ecc\CMakeFiles\__idf_micro-ecc.dir
  TARGET_COMPILE_PDB = esp-idf\micro-ecc\CMakeFiles\__idf_micro-ecc.dir\__idf_micro-ecc.pdb
  TARGET_PDB = esp-idf\micro-ecc\libmicro-ecc.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_micro-ecc


#############################################
# Link the static library esp-idf\micro-ecc\libmicro-ecc.a

build esp-idf/micro-ecc/libmicro-ecc.a: C_STATIC_LIBRARY_LINKER____idf_micro-ecc_ esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj || esp-idf/spi_flash/libspi_flash.a
  LANGUAGE_COMPILE_FLAGS = -march=rv32imc
  OBJECT_DIR = esp-idf\micro-ecc\CMakeFiles\__idf_micro-ecc.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\micro-ecc\CMakeFiles\__idf_micro-ecc.dir\__idf_micro-ecc.pdb
  TARGET_FILE = esp-idf\micro-ecc\libmicro-ecc.a
  TARGET_PDB = esp-idf\micro-ecc\libmicro-ecc.pdb


#############################################
# Utility command for edit_cache

build esp-idf/micro-ecc/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\micro-ecc && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/micro-ecc/edit_cache: phony esp-idf/micro-ecc/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/micro-ecc/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\micro-ecc && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/micro-ecc/rebuild_cache: phony esp-idf/micro-ecc/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_spi_flash


#############################################
# Order-only phony target for __idf_spi_flash

build cmake_object_order_depends_target___idf_spi_flash: phony || cmake_object_order_depends_target___idf_bootloader_support

build esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/esp32c3/spi_flash_rom_patch.c.obj: C_COMPILER____idf_spi_flash_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/esp32c3/spi_flash_rom_patch.c || cmake_object_order_depends_target___idf_spi_flash
  DEP_FILE = esp-idf\spi_flash\CMakeFiles\__idf_spi_flash.dir\esp32c3\spi_flash_rom_patch.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include/spi_flash -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader
  OBJECT_DIR = esp-idf\spi_flash\CMakeFiles\__idf_spi_flash.dir
  OBJECT_FILE_DIR = esp-idf\spi_flash\CMakeFiles\__idf_spi_flash.dir\esp32c3
  TARGET_COMPILE_PDB = esp-idf\spi_flash\CMakeFiles\__idf_spi_flash.dir\__idf_spi_flash.pdb
  TARGET_PDB = esp-idf\spi_flash\libspi_flash.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_spi_flash


#############################################
# Link the static library esp-idf\spi_flash\libspi_flash.a

build esp-idf/spi_flash/libspi_flash.a: C_STATIC_LIBRARY_LINKER____idf_spi_flash_ esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/esp32c3/spi_flash_rom_patch.c.obj || esp-idf/bootloader_support/libbootloader_support.a
  LANGUAGE_COMPILE_FLAGS = -march=rv32imc
  OBJECT_DIR = esp-idf\spi_flash\CMakeFiles\__idf_spi_flash.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\spi_flash\CMakeFiles\__idf_spi_flash.dir\__idf_spi_flash.pdb
  TARGET_FILE = esp-idf\spi_flash\libspi_flash.a
  TARGET_PDB = esp-idf\spi_flash\libspi_flash.pdb


#############################################
# Utility command for edit_cache

build esp-idf/spi_flash/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\spi_flash && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/spi_flash/edit_cache: phony esp-idf/spi_flash/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/spi_flash/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\spi_flash && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/spi_flash/rebuild_cache: phony esp-idf/spi_flash/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_bootloader_support


#############################################
# Order-only phony target for __idf_bootloader_support

build cmake_object_order_depends_target___idf_bootloader_support: phony || cmake_object_order_depends_target___idf_efuse

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/bootloader_common.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_common.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/bootloader_common_loader.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_common_loader.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/bootloader_clock_init.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_clock_init.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_flash.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/bootloader_flash.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_flash.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/bootloader_mem.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_mem.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/bootloader_random.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_random.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c3.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/bootloader_random_esp32c3.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_random_esp32c3.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/bootloader_utility.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_utility.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/esp_image_format.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\esp_image_format.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/flash_encrypt.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\flash_encrypt.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/secure_boot.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\secure_boot.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/flash_partitions.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\flash_partitions.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_qio_mode.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/flash_qio_mode.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\flash_qio_mode.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_flash_config_esp32c3.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/bootloader_flash_config_esp32c3.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_flash_config_esp32c3.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse_esp32c3.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/bootloader_efuse_esp32c3.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_efuse_esp32c3.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/bootloader_init.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_init.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/bootloader_clock_loader.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_clock_loader.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/bootloader_console.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_console.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/bootloader_console_loader.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_console_loader.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/bootloader_panic.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_panic.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_sha.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/esp32c3/bootloader_sha.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\esp32c3\bootloader_sha.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\esp32c3
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_soc.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/esp32c3/bootloader_soc.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\esp32c3\bootloader_soc.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\esp32c3
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_esp32c3.c.obj: C_COMPILER____idf_bootloader_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/src/esp32c3/bootloader_esp32c3.c || cmake_object_order_depends_target___idf_bootloader_support
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\esp32c3\bootloader_esp32c3.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\esp32c3
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_bootloader_support


#############################################
# Link the static library esp-idf\bootloader_support\libbootloader_support.a

build esp-idf/bootloader_support/libbootloader_support.a: C_STATIC_LIBRARY_LINKER____idf_bootloader_support_ esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_flash.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c3.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_qio_mode.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_flash_config_esp32c3.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse_esp32c3.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_sha.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_soc.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_esp32c3.c.obj || esp-idf/efuse/libefuse.a
  LANGUAGE_COMPILE_FLAGS = -march=rv32imc
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_FILE = esp-idf\bootloader_support\libbootloader_support.a
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb


#############################################
# Utility command for edit_cache

build esp-idf/bootloader_support/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\bootloader_support && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/bootloader_support/edit_cache: phony esp-idf/bootloader_support/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/bootloader_support/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\bootloader_support && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/bootloader_support/rebuild_cache: phony esp-idf/bootloader_support/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for efuse_test_table

build esp-idf/efuse/efuse_test_table: phony esp-idf/efuse/CMakeFiles/efuse_test_table

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_efuse


#############################################
# Order-only phony target for __idf_efuse

build cmake_object_order_depends_target___idf_efuse: phony || cmake_object_order_depends_target___idf_esp_system

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_table.c.obj: C_COMPILER____idf_efuse_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/esp_efuse_table.c || cmake_object_order_depends_target___idf_efuse
  DEP_FILE = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\esp32c3\esp_efuse_table.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\esp32c3
  TARGET_COMPILE_PDB = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\__idf_efuse.pdb
  TARGET_PDB = esp-idf\efuse\libefuse.pdb

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_fields.c.obj: C_COMPILER____idf_efuse_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/esp_efuse_fields.c || cmake_object_order_depends_target___idf_efuse
  DEP_FILE = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\esp32c3\esp_efuse_fields.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\esp32c3
  TARGET_COMPILE_PDB = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\__idf_efuse.pdb
  TARGET_PDB = esp-idf\efuse\libefuse.pdb

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_rtc_calib.c.obj: C_COMPILER____idf_efuse_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/esp_efuse_rtc_calib.c || cmake_object_order_depends_target___idf_efuse
  DEP_FILE = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\esp32c3\esp_efuse_rtc_calib.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\esp32c3
  TARGET_COMPILE_PDB = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\__idf_efuse.pdb
  TARGET_PDB = esp-idf\efuse\libefuse.pdb

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_utility.c.obj: C_COMPILER____idf_efuse_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/esp_efuse_utility.c || cmake_object_order_depends_target___idf_efuse
  DEP_FILE = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\esp32c3\esp_efuse_utility.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\esp32c3
  TARGET_COMPILE_PDB = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\__idf_efuse.pdb
  TARGET_PDB = esp-idf\efuse\libefuse.pdb

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj: C_COMPILER____idf_efuse_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/src/esp_efuse_api.c || cmake_object_order_depends_target___idf_efuse
  DEP_FILE = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\src\esp_efuse_api.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\src
  TARGET_COMPILE_PDB = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\__idf_efuse.pdb
  TARGET_PDB = esp-idf\efuse\libefuse.pdb

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj: C_COMPILER____idf_efuse_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/src/esp_efuse_fields.c || cmake_object_order_depends_target___idf_efuse
  DEP_FILE = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\src\esp_efuse_fields.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\src
  TARGET_COMPILE_PDB = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\__idf_efuse.pdb
  TARGET_PDB = esp-idf\efuse\libefuse.pdb

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj: C_COMPILER____idf_efuse_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/src/esp_efuse_utility.c || cmake_object_order_depends_target___idf_efuse
  DEP_FILE = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\src\esp_efuse_utility.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\src
  TARGET_COMPILE_PDB = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\__idf_efuse.pdb
  TARGET_PDB = esp-idf\efuse\libefuse.pdb

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api_key_esp32xx.c.obj: C_COMPILER____idf_efuse_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/src/esp_efuse_api_key_esp32xx.c || cmake_object_order_depends_target___idf_efuse
  DEP_FILE = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\src\esp_efuse_api_key_esp32xx.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\src
  TARGET_COMPILE_PDB = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\__idf_efuse.pdb
  TARGET_PDB = esp-idf\efuse\libefuse.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_efuse


#############################################
# Link the static library esp-idf\efuse\libefuse.a

build esp-idf/efuse/libefuse.a: C_STATIC_LIBRARY_LINKER____idf_efuse_ esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_table.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_fields.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_rtc_calib.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_utility.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api_key_esp32xx.c.obj || esp-idf/esp_system/libesp_system.a
  LANGUAGE_COMPILE_FLAGS = -march=rv32imc
  OBJECT_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\__idf_efuse.pdb
  TARGET_FILE = esp-idf\efuse\libefuse.a
  TARGET_PDB = esp-idf\efuse\libefuse.pdb


#############################################
# Utility command for efuse-common-table

build esp-idf/efuse/efuse-common-table: phony esp-idf/efuse/CMakeFiles/efuse-common-table


#############################################
# Utility command for efuse_common_table

build esp-idf/efuse/efuse_common_table: phony esp-idf/efuse/CMakeFiles/efuse_common_table esp-idf/efuse/efuse-common-table


#############################################
# Utility command for efuse-custom-table

build esp-idf/efuse/efuse-custom-table: phony


#############################################
# Utility command for show_efuse_table

build esp-idf/efuse/show_efuse_table: phony esp-idf/efuse/CMakeFiles/show_efuse_table esp-idf/efuse/show-efuse-table


#############################################
# Utility command for efuse_custom_table

build esp-idf/efuse/efuse_custom_table: phony esp-idf/efuse/CMakeFiles/efuse_custom_table esp-idf/efuse/efuse-custom-table


#############################################
# Utility command for show-efuse-table

build esp-idf/efuse/show-efuse-table: phony esp-idf/efuse/CMakeFiles/show-efuse-table


#############################################
# Utility command for edit_cache

build esp-idf/efuse/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\efuse && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/efuse/edit_cache: phony esp-idf/efuse/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/efuse/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\efuse && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/efuse/rebuild_cache: phony esp-idf/efuse/CMakeFiles/rebuild_cache.util


#############################################
# Custom command for esp-idf\efuse\CMakeFiles\efuse_test_table

build esp-idf/efuse/CMakeFiles/efuse_test_table: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\efuse && F:\kfa\Espressif\python_env\idf4.4_py3.8_env\Scripts\python.exe F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/efuse_table_gen.py F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/test/esp_efuse_test_table.csv -t esp32c3 --max_blk_len 256"


#############################################
# Custom command for esp-idf\efuse\CMakeFiles\efuse-common-table

build esp-idf/efuse/CMakeFiles/efuse-common-table: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\efuse && F:\kfa\Espressif\python_env\idf4.4_py3.8_env\Scripts\python.exe F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/efuse_table_gen.py F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/esp_efuse_table.csv -t esp32c3 --max_blk_len 256"


#############################################
# Custom command for esp-idf\efuse\CMakeFiles\efuse_common_table

build esp-idf/efuse/CMakeFiles/efuse_common_table: CUSTOM_COMMAND || esp-idf/efuse/efuse-common-table
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\efuse && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe -E echo Warning: Command "efuse_common_table" is deprecated and will be removed in the next major release.         Please use "efuse-common-table" instead."


#############################################
# Custom command for esp-idf\efuse\CMakeFiles\show_efuse_table

build esp-idf/efuse/CMakeFiles/show_efuse_table: CUSTOM_COMMAND || esp-idf/efuse/show-efuse-table
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\efuse && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe -E echo Warning: Command "show_efuse_table" is deprecated and will be removed in the next major release.         Please use "show-efuse-table" instead."


#############################################
# Custom command for esp-idf\efuse\CMakeFiles\efuse_custom_table

build esp-idf/efuse/CMakeFiles/efuse_custom_table: CUSTOM_COMMAND || esp-idf/efuse/efuse-custom-table
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\efuse && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe -E echo Warning: Command "efuse_custom_table" is deprecated and will be removed in the next major release.         Please use "efuse-custom-table" instead."


#############################################
# Custom command for esp-idf\efuse\CMakeFiles\show-efuse-table

build esp-idf/efuse/CMakeFiles/show-efuse-table: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\efuse && F:\kfa\Espressif\python_env\idf4.4_py3.8_env\Scripts\python.exe F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/efuse_table_gen.py F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/esp_efuse_table.csv -t esp32c3 --max_blk_len 256 --info"

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for rebuild_cache

build esp-idf/esp_system/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\esp_system && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esp_system/rebuild_cache: phony esp-idf/esp_system/CMakeFiles/rebuild_cache.util

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_esp_system


#############################################
# Order-only phony target for __idf_esp_system

build cmake_object_order_depends_target___idf_esp_system: phony || cmake_object_order_depends_target___idf_esp_hw_support

build esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj: C_COMPILER____idf_esp_system_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_system/esp_err.c || cmake_object_order_depends_target___idf_esp_system
  DEP_FILE = esp-idf\esp_system\CMakeFiles\__idf_esp_system.dir\esp_err.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\esp_system\CMakeFiles\__idf_esp_system.dir
  OBJECT_FILE_DIR = esp-idf\esp_system\CMakeFiles\__idf_esp_system.dir
  TARGET_COMPILE_PDB = esp-idf\esp_system\CMakeFiles\__idf_esp_system.dir\__idf_esp_system.pdb
  TARGET_PDB = esp-idf\esp_system\libesp_system.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_esp_system


#############################################
# Link the static library esp-idf\esp_system\libesp_system.a

build esp-idf/esp_system/libesp_system.a: C_STATIC_LIBRARY_LINKER____idf_esp_system_ esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj || esp-idf/esp_hw_support/libesp_hw_support.a
  LANGUAGE_COMPILE_FLAGS = -march=rv32imc
  OBJECT_DIR = esp-idf\esp_system\CMakeFiles\__idf_esp_system.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\esp_system\CMakeFiles\__idf_esp_system.dir\__idf_esp_system.pdb
  TARGET_FILE = esp-idf\esp_system\libesp_system.a
  TARGET_PDB = esp-idf\esp_system\libesp_system.pdb


#############################################
# Utility command for edit_cache

build esp-idf/esp_system/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\esp_system && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esp_system/edit_cache: phony esp-idf/esp_system/CMakeFiles/edit_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_esp_hw_support


#############################################
# Order-only phony target for __idf_esp_hw_support

build cmake_object_order_depends_target___idf_esp_hw_support: phony || cmake_object_order_depends_target___idf_esp_common

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/compare_set.c.obj: C_COMPILER____idf_esp_hw_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/compare_set.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\compare_set.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu_util.c.obj: C_COMPILER____idf_esp_hw_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/cpu_util.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\cpu_util.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/cpu_util_esp32c3.c.obj: C_COMPILER____idf_esp_hw_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/cpu_util_esp32c3.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32c3\cpu_util_esp32c3.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32c3
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk_init.c.obj: C_COMPILER____idf_esp_hw_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/rtc_clk_init.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32c3\rtc_clk_init.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32c3
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk.c.obj: C_COMPILER____idf_esp_hw_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/rtc_clk.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32c3\rtc_clk.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32c3
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_init.c.obj: C_COMPILER____idf_esp_hw_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/rtc_init.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32c3\rtc_init.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32c3
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_pm.c.obj: C_COMPILER____idf_esp_hw_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/rtc_pm.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32c3\rtc_pm.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32c3
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_sleep.c.obj: C_COMPILER____idf_esp_hw_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/rtc_sleep.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32c3\rtc_sleep.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32c3
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_time.c.obj: C_COMPILER____idf_esp_hw_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/rtc_time.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32c3\rtc_time.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32c3
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/chip_info.c.obj: C_COMPILER____idf_esp_hw_support_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/chip_info.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32c3\chip_info.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32c3
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_esp_hw_support


#############################################
# Link the static library esp-idf\esp_hw_support\libesp_hw_support.a

build esp-idf/esp_hw_support/libesp_hw_support.a: C_STATIC_LIBRARY_LINKER____idf_esp_hw_support_ esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/compare_set.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu_util.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/cpu_util_esp32c3.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk_init.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_init.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_pm.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_sleep.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_time.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/chip_info.c.obj || esp-idf/esp_common/libesp_common.a
  LANGUAGE_COMPILE_FLAGS = -march=rv32imc
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_FILE = esp-idf\esp_hw_support\libesp_hw_support.a
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb


#############################################
# Utility command for edit_cache

build esp-idf/esp_hw_support/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\esp_hw_support && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esp_hw_support/edit_cache: phony esp-idf/esp_hw_support/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esp_hw_support/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\esp_hw_support && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esp_hw_support/rebuild_cache: phony esp-idf/esp_hw_support/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/esp_hw_support/port/esp32c3/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\esp_hw_support\port\esp32c3 && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esp_hw_support/port/esp32c3/edit_cache: phony esp-idf/esp_hw_support/port/esp32c3/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esp_hw_support/port/esp32c3/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\esp_hw_support\port\esp32c3 && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esp_hw_support/port/esp32c3/rebuild_cache: phony esp-idf/esp_hw_support/port/esp32c3/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/riscv/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\riscv && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/riscv/edit_cache: phony esp-idf/riscv/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/riscv/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\riscv && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/riscv/rebuild_cache: phony esp-idf/riscv/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/esp32c3/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\esp32c3 && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esp32c3/edit_cache: phony esp-idf/esp32c3/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esp32c3/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\esp32c3 && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esp32c3/rebuild_cache: phony esp-idf/esp32c3/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for rebuild_cache

build esp-idf/esp_common/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\esp_common && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esp_common/rebuild_cache: phony esp-idf/esp_common/CMakeFiles/rebuild_cache.util

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_esp_common


#############################################
# Order-only phony target for __idf_esp_common

build cmake_object_order_depends_target___idf_esp_common: phony || cmake_object_order_depends_target___idf_esp_rom

build esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj: C_COMPILER____idf_esp_common_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/src/esp_err_to_name.c || cmake_object_order_depends_target___idf_esp_common
  DEP_FILE = esp-idf\esp_common\CMakeFiles\__idf_esp_common.dir\src\esp_err_to_name.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include
  OBJECT_DIR = esp-idf\esp_common\CMakeFiles\__idf_esp_common.dir
  OBJECT_FILE_DIR = esp-idf\esp_common\CMakeFiles\__idf_esp_common.dir\src
  TARGET_COMPILE_PDB = esp-idf\esp_common\CMakeFiles\__idf_esp_common.dir\__idf_esp_common.pdb
  TARGET_PDB = esp-idf\esp_common\libesp_common.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_esp_common


#############################################
# Link the static library esp-idf\esp_common\libesp_common.a

build esp-idf/esp_common/libesp_common.a: C_STATIC_LIBRARY_LINKER____idf_esp_common_ esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj || esp-idf/esp_rom/libesp_rom.a
  LANGUAGE_COMPILE_FLAGS = -march=rv32imc
  OBJECT_DIR = esp-idf\esp_common\CMakeFiles\__idf_esp_common.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\esp_common\CMakeFiles\__idf_esp_common.dir\__idf_esp_common.pdb
  TARGET_FILE = esp-idf\esp_common\libesp_common.a
  TARGET_PDB = esp-idf\esp_common\libesp_common.pdb


#############################################
# Utility command for edit_cache

build esp-idf/esp_common/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\esp_common && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esp_common/edit_cache: phony esp-idf/esp_common/CMakeFiles/edit_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_esp_rom


#############################################
# Order-only phony target for __idf_esp_rom

build cmake_object_order_depends_target___idf_esp_rom: phony || cmake_object_order_depends_target___idf_log

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj: C_COMPILER____idf_esp_rom_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/patches/esp_rom_crc.c || cmake_object_order_depends_target___idf_esp_rom
  DEP_FILE = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches\esp_rom_crc.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include
  OBJECT_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches
  TARGET_COMPILE_PDB = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\__idf_esp_rom.pdb
  TARGET_PDB = esp-idf\esp_rom\libesp_rom.pdb

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj: C_COMPILER____idf_esp_rom_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/patches/esp_rom_sys.c || cmake_object_order_depends_target___idf_esp_rom
  DEP_FILE = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches\esp_rom_sys.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include
  OBJECT_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches
  TARGET_COMPILE_PDB = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\__idf_esp_rom.pdb
  TARGET_PDB = esp-idf\esp_rom\libesp_rom.pdb

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj: C_COMPILER____idf_esp_rom_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/patches/esp_rom_uart.c || cmake_object_order_depends_target___idf_esp_rom
  DEP_FILE = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches\esp_rom_uart.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include
  OBJECT_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches
  TARGET_COMPILE_PDB = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\__idf_esp_rom.pdb
  TARGET_PDB = esp-idf\esp_rom\libesp_rom.pdb

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_tjpgd.c.obj: C_COMPILER____idf_esp_rom_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/patches/esp_rom_tjpgd.c || cmake_object_order_depends_target___idf_esp_rom
  DEP_FILE = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches\esp_rom_tjpgd.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include
  OBJECT_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches
  TARGET_COMPILE_PDB = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\__idf_esp_rom.pdb
  TARGET_PDB = esp-idf\esp_rom\libesp_rom.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_esp_rom


#############################################
# Link the static library esp-idf\esp_rom\libesp_rom.a

build esp-idf/esp_rom/libesp_rom.a: C_STATIC_LIBRARY_LINKER____idf_esp_rom_ esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_tjpgd.c.obj || esp-idf/log/liblog.a
  LANGUAGE_COMPILE_FLAGS = -march=rv32imc
  OBJECT_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\__idf_esp_rom.pdb
  TARGET_FILE = esp-idf\esp_rom\libesp_rom.a
  TARGET_PDB = esp-idf\esp_rom\libesp_rom.pdb


#############################################
# Utility command for edit_cache

build esp-idf/esp_rom/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\esp_rom && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esp_rom/edit_cache: phony esp-idf/esp_rom/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esp_rom/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\esp_rom && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esp_rom/rebuild_cache: phony esp-idf/esp_rom/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_log


#############################################
# Order-only phony target for __idf_log

build cmake_object_order_depends_target___idf_log: phony || esp-idf/log/CMakeFiles/__idf_log.dir

build esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj: C_COMPILER____idf_log_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/log.c || cmake_object_order_depends_target___idf_log
  DEP_FILE = esp-idf\log\CMakeFiles\__idf_log.dir\log.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include
  OBJECT_DIR = esp-idf\log\CMakeFiles\__idf_log.dir
  OBJECT_FILE_DIR = esp-idf\log\CMakeFiles\__idf_log.dir
  TARGET_COMPILE_PDB = esp-idf\log\CMakeFiles\__idf_log.dir\__idf_log.pdb
  TARGET_PDB = esp-idf\log\liblog.pdb

build esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj: C_COMPILER____idf_log_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/log_buffers.c || cmake_object_order_depends_target___idf_log
  DEP_FILE = esp-idf\log\CMakeFiles\__idf_log.dir\log_buffers.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include
  OBJECT_DIR = esp-idf\log\CMakeFiles\__idf_log.dir
  OBJECT_FILE_DIR = esp-idf\log\CMakeFiles\__idf_log.dir
  TARGET_COMPILE_PDB = esp-idf\log\CMakeFiles\__idf_log.dir\__idf_log.pdb
  TARGET_PDB = esp-idf\log\liblog.pdb

build esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj: C_COMPILER____idf_log_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/log_noos.c || cmake_object_order_depends_target___idf_log
  DEP_FILE = esp-idf\log\CMakeFiles\__idf_log.dir\log_noos.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include
  OBJECT_DIR = esp-idf\log\CMakeFiles\__idf_log.dir
  OBJECT_FILE_DIR = esp-idf\log\CMakeFiles\__idf_log.dir
  TARGET_COMPILE_PDB = esp-idf\log\CMakeFiles\__idf_log.dir\__idf_log.pdb
  TARGET_PDB = esp-idf\log\liblog.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_log


#############################################
# Link the static library esp-idf\log\liblog.a

build esp-idf/log/liblog.a: C_STATIC_LIBRARY_LINKER____idf_log_ esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj
  LANGUAGE_COMPILE_FLAGS = -march=rv32imc
  OBJECT_DIR = esp-idf\log\CMakeFiles\__idf_log.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\log\CMakeFiles\__idf_log.dir\__idf_log.pdb
  TARGET_FILE = esp-idf\log\liblog.a
  TARGET_PDB = esp-idf\log\liblog.pdb


#############################################
# Utility command for edit_cache

build esp-idf/log/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\log && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/log/edit_cache: phony esp-idf/log/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/log/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\log && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/log/rebuild_cache: phony esp-idf/log/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for bootloader_check_size

build esp-idf/esptool_py/bootloader_check_size: phony esp-idf/esptool_py/CMakeFiles/bootloader_check_size gen_project_binary


#############################################
# Utility command for edit_cache

build esp-idf/esptool_py/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\esptool_py && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esptool_py/edit_cache: phony esp-idf/esptool_py/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esptool_py/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\esptool_py && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esptool_py/rebuild_cache: phony esp-idf/esptool_py/CMakeFiles/rebuild_cache.util


#############################################
# Custom command for esp-idf\esptool_py\CMakeFiles\bootloader_check_size

build esp-idf/esptool_py/CMakeFiles/bootloader_check_size: CUSTOM_COMMAND || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/spi_flash/libspi_flash.a gen_project_binary
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\esptool_py && F:\kfa\Espressif\python_env\idf4.4_py3.8_env\Scripts\python.exe F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 F:/kfa/codexm/station-code/build/bootloader/bootloader.bin"

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/partition_table/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\partition_table && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/partition_table/edit_cache: phony esp-idf/partition_table/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/partition_table/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\partition_table && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/partition_table/rebuild_cache: phony esp-idf/partition_table/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/bootloader/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\bootloader && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/bootloader/edit_cache: phony esp-idf/bootloader/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/bootloader/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\bootloader && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/bootloader/rebuild_cache: phony esp-idf/bootloader/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/freertos/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\freertos && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/freertos/edit_cache: phony esp-idf/freertos/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/freertos/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\freertos && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/freertos/rebuild_cache: phony esp-idf/freertos/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/main/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\main && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake-gui.exe -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/main/edit_cache: phony esp-idf/main/CMakeFiles/edit_cache.util

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_main


#############################################
# Order-only phony target for __idf_main

build cmake_object_order_depends_target___idf_main: phony || cmake_object_order_depends_target___idf_hal

build esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj: C_COMPILER____idf_main_ F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/main/bootloader_start.c || cmake_object_order_depends_target___idf_main
  DEP_FILE = esp-idf\main\CMakeFiles\__idf_main.dir\bootloader_start.c.obj.d
  FLAGS = -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v4.4.1-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader
  OBJECT_DIR = esp-idf\main\CMakeFiles\__idf_main.dir
  OBJECT_FILE_DIR = esp-idf\main\CMakeFiles\__idf_main.dir
  TARGET_COMPILE_PDB = esp-idf\main\CMakeFiles\__idf_main.dir\__idf_main.pdb
  TARGET_PDB = esp-idf\main\libmain.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_main


#############################################
# Link the static library esp-idf\main\libmain.a

build esp-idf/main/libmain.a: C_STATIC_LIBRARY_LINKER____idf_main_ esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj || esp-idf/hal/libhal.a
  LANGUAGE_COMPILE_FLAGS = -march=rv32imc
  OBJECT_DIR = esp-idf\main\CMakeFiles\__idf_main.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\main\CMakeFiles\__idf_main.dir\__idf_main.pdb
  TARGET_FILE = esp-idf\main\libmain.a
  TARGET_PDB = esp-idf\main\libmain.pdb


#############################################
# Utility command for rebuild_cache

build esp-idf/main/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\kfa\codexm\station-code\build\bootloader\esp-idf\main && F:\kfa\Espressif\tools\cmake\3.20.3\bin\cmake.exe --regenerate-during-build -SF:\kfa\Espressif\frameworks\esp-idf-v4.4.1\components\bootloader\subproject -BF:\kfa\codexm\station-code\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/main/rebuild_cache: phony esp-idf/main/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build __idf_bootloader_support: phony esp-idf/bootloader_support/libbootloader_support.a

build __idf_efuse: phony esp-idf/efuse/libefuse.a

build __idf_esp_common: phony esp-idf/esp_common/libesp_common.a

build __idf_esp_hw_support: phony esp-idf/esp_hw_support/libesp_hw_support.a

build __idf_esp_rom: phony esp-idf/esp_rom/libesp_rom.a

build __idf_esp_system: phony esp-idf/esp_system/libesp_system.a

build __idf_hal: phony esp-idf/hal/libhal.a

build __idf_log: phony esp-idf/log/liblog.a

build __idf_main: phony esp-idf/main/libmain.a

build __idf_micro-ecc: phony esp-idf/micro-ecc/libmicro-ecc.a

build __idf_soc: phony esp-idf/soc/libsoc.a

build __idf_spi_flash: phony esp-idf/spi_flash/libspi_flash.a

build bootloader_check_size: phony esp-idf/esptool_py/bootloader_check_size

build efuse-common-table: phony esp-idf/efuse/efuse-common-table

build efuse-custom-table: phony esp-idf/efuse/efuse-custom-table

build efuse_common_table: phony esp-idf/efuse/efuse_common_table

build efuse_custom_table: phony esp-idf/efuse/efuse_custom_table

build efuse_test_table: phony esp-idf/efuse/efuse_test_table

build libbootloader_support.a: phony esp-idf/bootloader_support/libbootloader_support.a

build libefuse.a: phony esp-idf/efuse/libefuse.a

build libesp_common.a: phony esp-idf/esp_common/libesp_common.a

build libesp_hw_support.a: phony esp-idf/esp_hw_support/libesp_hw_support.a

build libesp_rom.a: phony esp-idf/esp_rom/libesp_rom.a

build libesp_system.a: phony esp-idf/esp_system/libesp_system.a

build libhal.a: phony esp-idf/hal/libhal.a

build liblog.a: phony esp-idf/log/liblog.a

build libmain.a: phony esp-idf/main/libmain.a

build libmicro-ecc.a: phony esp-idf/micro-ecc/libmicro-ecc.a

build libsoc.a: phony esp-idf/soc/libsoc.a

build libspi_flash.a: phony esp-idf/spi_flash/libspi_flash.a

build show-efuse-table: phony esp-idf/efuse/show-efuse-table

build show_efuse_table: phony esp-idf/efuse/show_efuse_table

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader

build all: phony app bootloader.elf esp-idf/all

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf

build esp-idf/all: phony esp-idf/newlib/all esp-idf/hal/all esp-idf/soc/all esp-idf/micro-ecc/all esp-idf/spi_flash/all esp-idf/bootloader_support/all esp-idf/efuse/all esp-idf/esp_system/all esp-idf/esp_hw_support/all esp-idf/riscv/all esp-idf/esp32c3/all esp-idf/esp_common/all esp-idf/esp_rom/all esp-idf/log/all esp-idf/esptool_py/all esp-idf/partition_table/all esp-idf/bootloader/all esp-idf/freertos/all esp-idf/main/all

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/bootloader

build esp-idf/bootloader/all: phony

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/bootloader_support

build esp-idf/bootloader_support/all: phony esp-idf/bootloader_support/libbootloader_support.a

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/efuse

build esp-idf/efuse/all: phony esp-idf/efuse/libefuse.a

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/esp32c3

build esp-idf/esp32c3/all: phony

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/esp_common

build esp-idf/esp_common/all: phony esp-idf/esp_common/libesp_common.a

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/esp_hw_support

build esp-idf/esp_hw_support/all: phony esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_hw_support/port/esp32c3/all

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/esp_hw_support/port/esp32c3

build esp-idf/esp_hw_support/port/esp32c3/all: phony

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/esp_rom

build esp-idf/esp_rom/all: phony esp-idf/esp_rom/libesp_rom.a

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/esp_system

build esp-idf/esp_system/all: phony esp-idf/esp_system/libesp_system.a

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/esptool_py

build esp-idf/esptool_py/all: phony

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/freertos

build esp-idf/freertos/all: phony

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/hal

build esp-idf/hal/all: phony esp-idf/hal/libhal.a

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/log

build esp-idf/log/all: phony esp-idf/log/liblog.a

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/main

build esp-idf/main/all: phony esp-idf/main/libmain.a

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/micro-ecc

build esp-idf/micro-ecc/all: phony esp-idf/micro-ecc/libmicro-ecc.a

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/newlib

build esp-idf/newlib/all: phony

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/partition_table

build esp-idf/partition_table/all: phony

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/riscv

build esp-idf/riscv/all: phony

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/soc

build esp-idf/soc/all: phony esp-idf/soc/libsoc.a esp-idf/soc/esp32c3/all

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/soc/esp32c3

build esp-idf/soc/esp32c3/all: phony

# =============================================================================

#############################################
# Folder: F:/kfa/codexm/station-code/build/bootloader/esp-idf/spi_flash

build esp-idf/spi_flash/all: phony esp-idf/spi_flash/libspi_flash.a

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | CMakeCache.txt CMakeFiles/3.20.3/CMakeASMCompiler.cmake CMakeFiles/3.20.3/CMakeCCompiler.cmake CMakeFiles/3.20.3/CMakeCXXCompiler.cmake CMakeFiles/3.20.3/CMakeSystem.cmake CMakeFiles/git-data/grabRef.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/asio/asio/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/bootloader/subproject/components/micro-ecc/micro-ecc/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/bt/controller/lib_esp32/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/bt/controller/lib_esp32c3_family/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/bt/host/nimble/nimble/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/cbor/tinycbor/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/cmock/CMock/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/coap/libcoap/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/esp_phy/lib/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/esp_wifi/lib/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/esptool_py/esptool/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/expat/expat/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/ieee802154/lib/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/json/cJSON/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/libsodium/libsodium/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/lwip/lwip/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/mbedtls/mbedtls/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/mqtt/esp-mqtt/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/nghttp/nghttp2/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/openthread/lib/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/openthread/openthread/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/protobuf-c/protobuf-c/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/spiffs/spiffs/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/tinyusb/tinyusb/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/unity/unity/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/examples/build_system/cmake/import_lib/main/lib/tinyxml2/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/examples/peripherals/secure_element/atecc608_ecdsa/components/esp-cryptoauthlib/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/asio/asio/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/project_include.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/main/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bt/controller/lib_esp32/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bt/controller/lib_esp32c3_family/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bt/host/nimble/nimble/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/cbor/tinycbor/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/cmock/CMock/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/coap/libcoap/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/sources.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp32c3/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp32c3/project_include.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/project_include.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/project_include.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_phy/lib/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_system/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esptool_py/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esptool_py/esptool/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esptool_py/project_include.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/expat/expat/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/freertos/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/ieee802154/lib/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/json/cJSON/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/libsodium/libsodium/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/lwip/lwip/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mqtt/esp-mqtt/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/nghttp/nghttp2/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/openthread/lib/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/openthread/openthread/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/partition_table/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/partition_table/project_include.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/protobuf-c/protobuf-c/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spiffs/spiffs/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/tinyusb/tinyusb/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/unity/unity/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/examples/build_system/cmake/import_lib/main/lib/tinyxml2/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/examples/peripherals/secure_element/atecc608_ecdsa/components/esp-cryptoauthlib/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/build.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/component.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/crosstool_version_check.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/dfu.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/git_submodules.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/idf.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/kconfig.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/ldgen.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/project.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/project_description.json.in F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/targets.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/third_party/GetGitRevisionDescription.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/third_party/GetGitRevisionDescription.cmake.in F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/toolchain-esp32c3.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/uf2.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/utilities.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/version.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/kconfig_new/confgen.py F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/kconfig_new/config.env.in F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeASMCompiler.cmake.in F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeASMInformation.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeCCompiler.cmake.in F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeCCompilerABI.c F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeCInformation.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeCXXCompiler.cmake.in F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeCXXCompilerABI.cpp F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeCXXInformation.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeCommonLanguageInclude.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeCompilerIdDetection.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeDetermineASMCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeDetermineCCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeDetermineCXXCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeDetermineCompileFeatures.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeDetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeDetermineCompilerABI.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeDetermineCompilerId.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeDetermineSystem.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeFindBinUtils.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeGenericSystem.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeInitializeConfigs.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeLanguageInformation.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeNinjaFindMake.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeParseImplicitIncludeInfo.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeParseImplicitLinkInfo.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeParseLibraryArchitecture.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeSystem.cmake.in F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeSystemSpecificInformation.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeSystemSpecificInitialize.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeTestASMCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeTestCCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeTestCXXCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeTestCompilerCommon.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/ADSP-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/ARMCC-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/ARMClang-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/AppleClang-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Borland-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Bruce-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/CMakeCommonCompilerMacros.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Clang-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Clang-DetermineCompilerInternal.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Compaq-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Cray-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Embarcadero-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Fujitsu-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/GHS-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/GNU-ASM.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/GNU-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/GNU-C.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/GNU-CXX.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/GNU-FindBinUtils.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/GNU.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/HP-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/HP-CXX-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/IAR-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Intel-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/MSVC-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/NVHPC-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/NVIDIA-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/PGI-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/PathScale-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/SCO-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/SDCC-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/SunPro-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/TI-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Watcom-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/XL-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/XL-CXX-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/XLClang-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/zOS-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/ExternalProject.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/FindGit.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/FindPackageHandleStandardArgs.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/FindPackageMessage.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Internal/FeatureTesting.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Platform/Generic.cmake F$:/kfa/codexm/station-code/sdkconfig config/sdkconfig.cmake config/sdkconfig.h
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/3.20.3/CMakeASMCompiler.cmake CMakeFiles/3.20.3/CMakeCCompiler.cmake CMakeFiles/3.20.3/CMakeCXXCompiler.cmake CMakeFiles/3.20.3/CMakeSystem.cmake CMakeFiles/git-data/grabRef.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/asio/asio/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/bootloader/subproject/components/micro-ecc/micro-ecc/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/bt/controller/lib_esp32/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/bt/controller/lib_esp32c3_family/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/bt/host/nimble/nimble/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/cbor/tinycbor/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/cmock/CMock/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/coap/libcoap/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/esp_phy/lib/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/esp_wifi/lib/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/esptool_py/esptool/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/expat/expat/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/ieee802154/lib/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/json/cJSON/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/libsodium/libsodium/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/lwip/lwip/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/mbedtls/mbedtls/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/mqtt/esp-mqtt/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/nghttp/nghttp2/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/openthread/lib/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/openthread/openthread/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/protobuf-c/protobuf-c/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/spiffs/spiffs/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/tinyusb/tinyusb/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/components/unity/unity/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/examples/build_system/cmake/import_lib/main/lib/tinyxml2/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/.git/modules/examples/peripherals/secure_element/atecc608_ecdsa/components/esp-cryptoauthlib/HEAD F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/asio/asio/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/project_include.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/main/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bt/controller/lib_esp32/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bt/controller/lib_esp32c3_family/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bt/host/nimble/nimble/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/cbor/tinycbor/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/cmock/CMock/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/coap/libcoap/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/sources.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp32c3/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp32c3/project_include.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/project_include.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/project_include.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_phy/lib/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_system/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esptool_py/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esptool_py/esptool/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esptool_py/project_include.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/expat/expat/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/freertos/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/ieee802154/lib/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/json/cJSON/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/libsodium/libsodium/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/lwip/lwip/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mqtt/esp-mqtt/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/nghttp/nghttp2/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/openthread/lib/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/openthread/openthread/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/partition_table/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/partition_table/project_include.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/protobuf-c/protobuf-c/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/CMakeLists.txt F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spiffs/spiffs/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/tinyusb/tinyusb/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/unity/unity/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/examples/build_system/cmake/import_lib/main/lib/tinyxml2/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/examples/peripherals/secure_element/atecc608_ecdsa/components/esp-cryptoauthlib/.git F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/build.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/component.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/crosstool_version_check.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/dfu.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/git_submodules.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/idf.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/kconfig.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/ldgen.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/project.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/project_description.json.in F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/targets.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/third_party/GetGitRevisionDescription.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/third_party/GetGitRevisionDescription.cmake.in F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/toolchain-esp32c3.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/uf2.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/utilities.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/cmake/version.cmake F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/kconfig_new/confgen.py F$:/kfa/Espressif/frameworks/esp-idf-v4.4.1/tools/kconfig_new/config.env.in F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeASMCompiler.cmake.in F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeASMInformation.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeCCompiler.cmake.in F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeCCompilerABI.c F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeCInformation.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeCXXCompiler.cmake.in F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeCXXCompilerABI.cpp F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeCXXInformation.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeCommonLanguageInclude.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeCompilerIdDetection.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeDetermineASMCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeDetermineCCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeDetermineCXXCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeDetermineCompileFeatures.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeDetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeDetermineCompilerABI.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeDetermineCompilerId.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeDetermineSystem.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeFindBinUtils.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeGenericSystem.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeInitializeConfigs.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeLanguageInformation.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeNinjaFindMake.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeParseImplicitIncludeInfo.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeParseImplicitLinkInfo.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeParseLibraryArchitecture.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeSystem.cmake.in F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeSystemSpecificInformation.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeSystemSpecificInitialize.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeTestASMCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeTestCCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeTestCXXCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/CMakeTestCompilerCommon.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/ADSP-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/ARMCC-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/ARMClang-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/AppleClang-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Borland-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Bruce-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/CMakeCommonCompilerMacros.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Clang-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Clang-DetermineCompilerInternal.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Compaq-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Cray-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Embarcadero-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Fujitsu-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/GHS-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/GNU-ASM.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/GNU-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/GNU-C.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/GNU-CXX.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/GNU-FindBinUtils.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/GNU.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/HP-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/HP-CXX-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/IAR-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Intel-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/MSVC-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/NVHPC-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/NVIDIA-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/PGI-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/PathScale-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/SCO-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/SDCC-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/SunPro-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/TI-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/Watcom-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/XL-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/XL-CXX-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/XLClang-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/zOS-C-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/ExternalProject.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/FindGit.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/FindPackageHandleStandardArgs.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/FindPackageMessage.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Internal/FeatureTesting.cmake F$:/kfa/Espressif/tools/cmake/3.20.3/share/cmake-3.20/Modules/Platform/Generic.cmake F$:/kfa/codexm/station-code/sdkconfig config/sdkconfig.cmake config/sdkconfig.h: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
