#ifndef _LED_H_
#define _LED_H_



//����LED�Ƶ�IO��
#define LED_WHITE_IO 		1  //��Ӧ��Ƶ�LED���̵�Ϊ7������Ϊ6
#define LED_YELLOW_IO 	    6  //��Ӧ���Ƶ�LED�����Ϊ10���̵�Ϊ7
#define BEEP_IO 	        0  //����beep

#define GPIO1_IO 	2  //��Ӧio2
#define GPIO2_IO 	3  //��Ӧio3
#define GPIO3_IO 	4  //��Ӧio4
#define GPIO4_IO    5  //��Ӧio5
#define RST_IO 	6  //��Ӧio6
#define EN_IO 	7  //��Ӧio7

//����LED״̬��BEEP״̬
#define LED_ON          0   //LED������ƽΪ�͵�ƽ
#define LED_OFF         1   //LED�����ƽΪ�ߵ�ƽ

#define BEEP_ON          0   //�ߵ�ƽ����
#define BEEP_OFF         1   //�͵�ƽ����

extern unsigned char		         PART_STATE_OFF;						    //��
extern unsigned char		         PART_STATE_ON;						    //��
extern unsigned char		         g_beep_count;						    
extern unsigned char		         g_whitle_count;						
extern unsigned char		         g_yellow_count;						
extern unsigned char                 g_beep_state;  


//LED��ʼ��
void _init_Led();
//���ư׵�
void _white_led_onoff(int state);
//
void _yellow_led__onoff(int state);
//���Ʒ�����
void _beep_onoff(int state);

//�����л�
void _switch_beep(unsigned char state,short time);
//�ƿ����л�
void _switch_white_led(unsigned char state,short time);
//����BEEP
void testbeep(short time,char cishu);


#endif