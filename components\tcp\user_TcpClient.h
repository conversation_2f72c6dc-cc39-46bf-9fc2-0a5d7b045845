#ifndef _USER_TCP_SERVER_H_
#define _USER_TCP_SERVER_H_

#include <string.h>
#include <sys/socket.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_wifi.h"
//#include "esp_event_loop.h"
#include "esp_event.h"
#include "esp_log.h"
#include "driver/uart.h"


#define TAG        "TCP_CLIENT"            //打印的tag

/**************************************数据定义**************************************/
#define             WIFI_CONNECTED_BIT               BIT0
#define				DEVID_LEN					     5
#define				SSID_LEN						 32
#define				PSWD_LEN			 			 64
#define				IPADDRESS_LEN				  	 16
#define				MAX_COUNT		        	     120		//标签个数
#define				TAG_LEN					   		 12			//标签数据长度
#define				PACKHEADER_LEN		   	    	 5			//除去数据后的通讯包头长度
#define             FRAME_LEN                        3          //帧头
#define             MAX_HEART_LEN                    30         //心跳数据包长度
#define             WORKING                          1
#define             STOP                             2
//基站的NET命令的参数值
enum scan_type_t    { enDAILY = 0x01, enFOCUS =  0x02, enUNUSED = 0x04 };
enum exec_state_t   { enDONING = 0x01, enCOMPLETE = 0x02, enIDLE = 0x04, enBEGIN = 0x08, enDEALING = 0x10} ;
enum is_state_t     { enYES = 0x01, enNO = 0x02 };
enum scan_way_1     { enNORMAL = 0x01,  enIGNORE = 0x02, enADD = 0x03, enSEPARATE = 0x04, enREDUCE =0x07, enMERGE = 0x08 };
enum scan_way_t     { enNandS = 0x05,  enIandS = 0x06, enNandM = 0x09, enIandM = 0x0a };

extern bool                          g_rxtx_need_restart;
extern struct       		 		 sockaddr_in             g_server_addr;  //server地址
extern ip4_addr_t        			 g_ip_addr;
extern ip4_addr_t        			 g_gw_addr;
extern ip4_addr_t           	     g_netmask_addr;
extern EventGroupHandle_t  		     g_tcp_event_group; 
extern int                           g_connect_socket;
extern char		                     g_swifi_ssid[SSID_LEN];			    //WIFI账号
extern char		                     g_swifi_password[PSWD_LEN];		    //WIFI密码
extern char		                     g_sserver_ip[IPADDRESS_LEN];		//服务器IP
extern unsigned short                g_server_port;					    //服务器端口
extern unsigned char                 g_beep_count;
/************************flash key value**************************/
ip4_addr_t        			         g_ip_addr;
ip4_addr_t        			         g_gw_addr;
ip4_addr_t           	             g_netmask_addr;


/*********************************************************/   
extern bool _receive_netdata( unsigned char* recdata, unsigned short index, unsigned short nlen );

//获取错误代码
int _get_socket_error_code(int socket);
//显示错误代码
int _show_socket_error_reason(const char *str, int socket);
//关闭socket
void _close_socket();
//接收并处理数据
void _client_recv_data(void *pvParameters);
//连接服务器
esp_err_t _create_tcp_client();
//建立网络连接，自动重连并读数据线程任务
void _tcp_client_connect(void *pvParameters);
//检查socket连接状态
bool _check_socket_connection(int socket_fd);



#endif
